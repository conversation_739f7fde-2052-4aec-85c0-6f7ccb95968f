apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: app-ndueplatform-ingress
  namespace: ndue-dev
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  ingressClassName: nginx
  tls:
    - hosts:
        - app-alb.ndueplatform-dev.test.a2a.it
      secretName: <da definire>
  rules:
    - host: app-alb.ndueplatform.com
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: my-app-service
                port:
                  number: 8080
