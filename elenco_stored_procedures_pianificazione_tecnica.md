# Elenco Stored Procedures - Processo di Pianificazione Tecnica

## Sistema NBDO - A2A
**Data:** 2025-01-15

---

## 1. PIANIFICAZIONE MANUTENZIONE (PMan)

### Gestione Interventi sui Gruppi
- `app.PManInterventiSuiGruppi.LoadInterventiPianificati` - Carica interventi pianificati
- `app.PManInterventiSuiGruppi.LoadInterventiNonAllocati` - Carica interventi non allocati

### Gestione Piani di Manutenzione
- `app.PManCancellazionePianoInBozza` - Cancella piano in bozza (impianto)
- `app.PManCancellazionePianoInBozzaSede` - Cancella piano in bozza (sede)
- `app.sp_P_Man_NuovaVersione` - Crea nuova versione piano manutenzione

### Gestione MOCC (Manutenzione Ordinaria Ciclica Centralizzata)
- `app.MOCC_GetElenco` - Ottiene elenco interventi MOCC

---

## 2. BUDGET COMBUSTIBILI

### Gestione Contratti e Piano Consegne
- `app.CombustibiliBudget.SALVA_T_BC_Contratti_PianoConsegne` - Salva contratti e piano consegne
- `app.CombustibiliBudget.GVC_SALVA_Contratti_PianoConsegne` - Gestione versione centrale
- `app.CombustibiliBudget.GestioneVCentrale_SALVA_Contratti_PianoConsegne` - Gestione versione centrale avanzata
- `app.CombustibiliBudget.GestioneContratto_SALVA_Contratti_PianoConsegne` - Gestione singolo contratto

### Creazione e Controlli Budget
- `app.CombustibiliBudget.CreazioneBudget_1` - Procedura principale creazione budget
- `app.CombustibiliBudget.CreazioneBudget_DatiDiConsuntivo` - Dati di consuntivo
- `app.CombustibiliBudget.CreazioneBudget_DatiDiVersione` - Dati di versione
- `app.CombustibiliBudget.CreazioneBudget_DatiManuali` - Dati manuali
- `app.CombustibiliBudget.Controlli` - Controlli generali
- `app.CombustibiliBudget.ControlloAccisa` - Controllo accise
- `app.CombustibiliBudget.ControlloPCICentrale` - Controllo PCI centrale
- `app.CombustibiliBudget.ControlloPrezzi` - Controllo prezzi
- `app.CombustibiliBudget.ControlloCambi` - Controllo cambi

### Calcoli e Salvataggi Specifici
- `app.CombustibiliBudget.Calcola_PMPComb` - Calcola PMP combustibili
- `app.CombustibiliBudget.SALVA_T_BC_ConsumiCombCentrale` - Salva consumi centrale
- `app.CombustibiliBudget.SALVA_T_BC_ConsumiCombUP` - Salva consumi UP
- `app.CombustibiliBudget.SALVA_T_BC_PMPComb` - Salva PMP combustibili
- `app.CombustibiliBudget.SALVA_T_BC_PrezziAcquistoComb` - Salva prezzi acquisto
- `app.CombustibiliBudget.SALVA_T_BC_CostoConsumiComb` - Salva costo consumi

### Gestione Versioni
- `app.CombustibiliBudget.GVE_NUOVA_VERSIONE` - Nuova versione
- `app.CombustibiliBudget.GVE_INSERISCIdaESISTENTE` - Inserisce da esistente
- `app.CombustibiliBudget.MapVersioni` - Mappa versioni
- `app.CombustibiliBudget.VersioniCentraliDelPeriodo` - Versioni centrali periodo

---

## 3. REPORTING E ANALISI

### Report Interventi Manutenzione
- `app.Reporting_GetInterventiManutenzione` - Report principale interventi
- `app.Reporting_GetInterventiManutenzioneCentraliTutte` - Tutte le centrali
- `app.Reporting_GetInterventiManutenzioneTermoTutte` - Tutte le termo
- `app.Reporting_GetInterventiManutenzioneIdroTutte` - Tutte le idro
- `app.Reporting_GetInterventiManutenzioneTermo` - Centrali termo specifiche
- `app.Reporting_GetInterventiManutenzioneIdro` - Centrali idro specifiche

### Report Analisi Specifiche
- `app.Reporting_InterventiManAnalisi` - Analisi interventi manutenzione
- `app.Reporting_InterventiTollingManAnalisi` - Analisi tolling manutenzione
- `app.Reporting_KPIBudgetVsManutenzione_Testata` - KPI Budget vs Manutenzione
- `app.Reporting_PianoManutAutomatico` - Piano manutenzione automatico

### Report KPI e Cruscotti
- `app.Reporting_KPICruscottoConsuntivoIOP&OR_Dati` - Dati cruscotto IOP&OR
- `app.Reporting_KPICruscottoConsuntivoIOP&OR_Testata` - Testata cruscotto
- `app.Reporting_KPICruscottoRiferimentoBudget_Testata` - Riferimento budget

---

## 4. GESTIONE INTERVENTI

### Selezione Interventi
- `app.SelezionaInterventi` - Selezione interventi generica
- `app.SelezionaInterventiByGruppi` - Selezione per gruppi
- `app.Conduzione.Sfiori.SelezionaInterventiByGruppo` - Selezione sfiori per gruppo

### Gestione Eventi
- `app.GetEventiManutenzioneByData` - Eventi manutenzione per data
- `app.GetLastEventiInterventoByData` - Ultimi eventi intervento

---

## 5. UNITÀ ESSENZIALI E MUST RUN

### Programmi Must Run
- `app.UnitaEssenziali.Elaborazioni.GetProgrammiMustRun` - Programmi Must Run
- `app.UnitaEssenziali.Elaborazioni.GetProveDiLegge` - Prove di legge
- `app.UnitaEssenziali_Reportistica_GetMustRun` - Report Must Run

---

## 6. UTILITY E SUPPORTO

### Utility Database
- `app.FindTabellePiene` - Trova tabelle con dati
- `app.ADM_SvuotaAnagrafiche` - Svuota anagrafiche (include tabelle PMan)

### Export e Integrazione
- `app.ExportSAP_EstrazioneStime_Tutte` - Export stime SAP
- `app.ExportSAP_EstrazioneStime_GME` - Export GME
- `app.ExportSAP_EstrazioneStime_Terna` - Export Terna
- `app.ExportSAP_EstrazioneStime_UESS` - Export UESS

---

## TABELLE PRINCIPALI COINVOLTE

### Pianificazione Manutenzione
- `T_P_Man_Testata_Sede` - Testata piani sede
- `T_P_Man_Testata_Centrale_Nucleo` - Testata piani centrale/nucleo
- `T_P_Man_Interventi_sui_Gruppi` - Interventi sui gruppi
- `T_PManTestataCentraleSede` - Collegamento testata centrale-sede
- `T_P_Man_SKEventoCollegate` - Schede evento collegate
- `T_P_Man_InterventiSfiori` - Sfiori interventi

### Budget Combustibili
- `T_BC_Contratti_PianoConsegne` - Contratti e piano consegne
- `T_BC_ContrattiCombustibile` - Contratti combustibili
- `T_BC_ConsumiCombCentrale` - Consumi combustibili centrale
- `T_BC_PMPComb` - PMP combustibili

### Must Run e Unità Essenziali
- `T_PgmMustRunTestata` - Testata programmi Must Run
- `T_PgmMustRun` - Programmi Must Run

---

## NOTE TECNICHE

1. **Prefisso PMan**: Identifica le procedure di Pianificazione Manutenzione
2. **Prefisso CombustibiliBudget**: Procedure per gestione budget combustibili
3. **Prefisso Reporting_**: Procedure per generazione report
4. **Schema app**: Tutte le procedure sono nello schema applicativo

## DIPENDENZE

- Molte procedure utilizzano `app.fn_ParseCommaDelimitedList` per parsing parametri
- Gestione UTC/Local time tramite funzioni `app.UtcToLocal` e `app.UtcAddHours`
- Integrazione con sistema stati tramite tabella `T_Stati`
