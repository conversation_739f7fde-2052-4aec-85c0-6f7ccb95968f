#!/usr/bin/env python3
import pyodbc
import subprocess
import pandas as pd

# ----------------------
# Parametri di connessione
# ----------------------
driver_path = '/opt/homebrew/lib/libmsodbcsql.18.dylib'  # Path assoluto al driver
server      = 'dctsvw035.group.local'
database    = 'nbdo'
username    = 'app'
password    = 'uis043Iy-H7HXJS_tW3Ise!CxT-w3l'

def fetch_tables():
    """
    Recupera schema e nome di tutte le tabelle utente dal database SQL Server.
    """
    conn_str = (
        f"DRIVER={driver_path};"
        f"SERVER={server};"
        f"DATABASE={database};"
        f"UID={username};"
        f"PWD={password};"
        "Encrypt=yes;TrustServerCertificate=yes"
    )
    conn = pyodbc.connect(conn_str, timeout=10)
    df = pd.read_sql("""
        SELECT TABLE_SCHEMA, TABLE_NAME
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_SCHEMA, TABLE_NAME;
    """, conn)
    conn.close()
    return df

def export_ddls(output_file='ddl_export.sql'):
    """
    Usa mssql-scripter per ogni tabella per generare il DDL CREATE TABLE
    e li accumula in un unico file di output.
    """
    tables = fetch_tables()
    with open(output_file, 'w', encoding='utf-8') as fout:
        for _, row in tables.iterrows():
            full_name = f"{row.TABLE_SCHEMA}.{row.TABLE_NAME}"
            print(f"Generazione DDL per {full_name}...")
            cmd = [
                'mssql-scripter',
                '--server', server,
                '--database', database,
                '--username', username,
                '--password', password,
                '--driver', driver_path,
                '--schema-only',
                '--include-objects', full_name
            ]
            proc = subprocess.run(cmd, capture_output=True, text=True)
            if proc.returncode != 0:
                print(f"⚠️ Errore scripting {full_name}: {proc.stderr.strip()}")
                continue
            fout.write(f"-- DDL per {full_name}\n")
            fout.write(proc.stdout)
            fout.write("\n\n")
    print(f"\n✅ Tutte le DDL sono state salvate in '{output_file}'")

if __name__ == "__main__":
    export_ddls()
