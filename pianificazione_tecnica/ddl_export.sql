-- Drop table

-- DROP TABLE nbdo.app.T_BDGDati;

CREATE TABLE nbdo.app.T_BDGDati (
	BDGDati_ID int IDENTITY(1,1) NOT NULL,
	BDGTestataCentrale_ID int NOT NULL,
	UnitaProduttive_ID int NOT NULL,
	Anno int NOT NULL,
	Mese int NOT NULL,
	AnagraficaKPI_ID int NOT NULL,
	ValoreProposta decimal(28,10) NULL,
	ValoreBudget decimal(28,10) NULL,
	CONSTRAINT IX_T_BDGDati UNIQUE (BDGTestataCentrale_ID,UnitaProduttive_ID,AnagraficaKPI_ID,Mese),
	CONSTRAINT PK_T_BDGDati PRIMARY KEY (BDGDati_ID),
	CONSTRAINT FK_T_BDGDati_T_BDGTestataCentrale FOREIGN KEY (BDGTestataCentrale_ID) REFERENCES nbdo.app.T_BDGTestataCentrale(BDGTestataCentrale_ID),
	CONSTRAINT FK_T_BDGDati_T_KPIAnagrafica FOREIGN KEY (AnagraficaKPI_ID) REFERENCES nbdo.app.T_KPIAnagrafica(AnagraficaKPI_ID),
	CONSTRAINT FK_T_BDGDati_T_UnitaProduttive FOREIGN KEY (UnitaProduttive_ID) REFERENCES nbdo.app.T_UnitaProduttive(UnitaProduttive_ID)
);
 CREATE NONCLUSTERED INDEX IX_T_BDGDati_KPI ON nbdo.app.T_BDGDati (  AnagraficaKPI_ID ASC  )  
	 WITH (  PAD_INDEX = OFF ,FILLFACTOR = 100  ,SORT_IN_TEMPDB = OFF , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ONLINE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON  )
	 ON [PRIMARY ] ;
 CREATE NONCLUSTERED INDEX IX_T_BDGDati_TestataCentrale ON nbdo.app.T_BDGDati (  BDGTestataCentrale_ID ASC  )  
	 WITH (  PAD_INDEX = OFF ,FILLFACTOR = 100  ,SORT_IN_TEMPDB = OFF , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ONLINE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON  )
	 ON [PRIMARY ] ;
 CREATE NONCLUSTERED INDEX IX_T_BDGDati_UP ON nbdo.app.T_BDGDati (  UnitaProduttive_ID ASC  )  
	 WITH (  PAD_INDEX = OFF ,FILLFACTOR = 100  ,SORT_IN_TEMPDB = OFF , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ONLINE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON  )
	 ON [PRIMARY ] ;
 CREATE UNIQUE NONCLUSTERED INDEX IX_T_BDGDati_UP_Anno_Mese_KPI ON nbdo.app.T_BDGDati (  UnitaProduttive_ID ASC  , Anno ASC  , Mese ASC  , AnagraficaKPI_ID ASC  )  
	 WITH (  PAD_INDEX = OFF ,FILLFACTOR = 100  ,SORT_IN_TEMPDB = OFF , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ONLINE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON  )
	 ON [PRIMARY ] ;



-- nbdo.app.T_BDGTestataCentrale definition

-- Drop table

-- DROP TABLE nbdo.app.T_BDGTestataCentrale;

CREATE TABLE nbdo.app.T_BDGTestataCentrale (
	BDGTestataCentrale_ID int IDENTITY(1,1) NOT NULL,
	BDGTestataSede_ID int NULL,
	Anno int NOT NULL,
	Versione int NOT NULL,
	Stati_ID int NULL,
	DataCreazione datetime NULL,
	DataApprovImpianto datetime NULL,
	DataApprovSede datetime NULL,
	UserCreazione int NULL,
	UserApprovImpianto int NULL,
	UserApprovSede int NULL,
	Impianti_ID int NULL,
	PdPInput int NULL,
	PdMInput int NULL,
	CONSTRAINT IX_T_BDGTestataCentrale UNIQUE (Anno,Versione,Impianti_ID),
	CONSTRAINT PK_BDG_ANN_Testata_Centrale PRIMARY KEY (BDGTestataCentrale_ID),
	CONSTRAINT FK_T_BDGTestataCentrale_T_BDGTestataSede FOREIGN KEY (BDGTestataSede_ID) REFERENCES nbdo.app.T_BDGTestataSede(BDGTestataSede_ID),
	CONSTRAINT FK_T_BDGTestataCentrale_T_Impianti FOREIGN KEY (Impianti_ID) REFERENCES nbdo.app.T_Impianti(Impianti_ID),
	CONSTRAINT FK_T_BDGTestataCentrale_T_Utenti FOREIGN KEY (UserApprovImpianto) REFERENCES nbdo.app.T_Utenti(Utenti_ID),
	CONSTRAINT FK_T_BDGTestataCentrale_T_Utenti1 FOREIGN KEY (UserApprovSede) REFERENCES nbdo.app.T_Utenti(Utenti_ID)
);
 CREATE UNIQUE NONCLUSTERED INDEX IX_T_BDGTestataCentrale_AnnoImpianto ON nbdo.app.T_BDGTestataCentrale (  Anno ASC  , Impianti_ID ASC  )  
	 WITH (  PAD_INDEX = OFF ,FILLFACTOR = 100  ,SORT_IN_TEMPDB = OFF , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ONLINE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON  )
	 ON [PRIMARY ] ;
 CREATE NONCLUSTERED INDEX IX_T_BDGTestataCentrale_Impianti_ID_FK ON nbdo.app.T_BDGTestataCentrale (  Impianti_ID ASC  )  
	 WITH (  PAD_INDEX = OFF ,FILLFACTOR = 100  ,SORT_IN_TEMPDB = OFF , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ONLINE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON  )
	 ON [PRIMARY ] ;
 CREATE NONCLUSTERED INDEX IX_T_BDGTestataCentrale_PdMInput ON nbdo.app.T_BDGTestataCentrale (  PdMInput ASC  )  
	 WITH (  PAD_INDEX = OFF ,FILLFACTOR = 100  ,SORT_IN_TEMPDB = OFF , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ONLINE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON  )
	 ON [PRIMARY ] ;
 CREATE NONCLUSTERED INDEX IX_T_BDGTestataCentrale_TestataSede ON nbdo.app.T_BDGTestataCentrale (  BDGTestataSede_ID ASC  )  
	 WITH (  PAD_INDEX = OFF ,FILLFACTOR = 100  ,SORT_IN_TEMPDB = OFF , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ONLINE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON  )
	 ON [PRIMARY ] ;
 CREATE NONCLUSTERED INDEX IX_T_BDGTestataCentrale_UserApprovImpianto_FK ON nbdo.app.T_BDGTestataCentrale (  UserApprovImpianto ASC  )  
	 WITH (  PAD_INDEX = OFF ,FILLFACTOR = 100  ,SORT_IN_TEMPDB = OFF , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ONLINE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON  )
	 ON [PRIMARY ] ;
 CREATE NONCLUSTERED INDEX IX_T_BDGTestataCentrale_UserApprovSede_FK ON nbdo.app.T_BDGTestataCentrale (  UserApprovSede ASC  )  
	 WITH (  PAD_INDEX = OFF ,FILLFACTOR = 100  ,SORT_IN_TEMPDB = OFF , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ONLINE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON  )
	 ON [PRIMARY ] ;


     -- nbdo.app.T_BDGTestataSede definition

-- Drop table

-- DROP TABLE nbdo.app.T_BDGTestataSede;

CREATE TABLE nbdo.app.T_BDGTestataSede (
	BDGTestataSede_ID int IDENTITY(1,1) NOT NULL,
	Anno int NOT NULL,
	Versione int DEFAULT 1 NOT NULL,
	Stati_ID int NULL,
	DataCreazione datetime NULL,
	DataApprovSede datetime NULL,
	DataApprovAFC datetime NULL,
	UserCreazione int NULL,
	UserApprovSede int NULL,
	UserApprovAFC int NULL,
	PdPInput int NULL,
	PdMInput int NULL,
	CONSTRAINT IX_T_BDGTestataSede UNIQUE (Anno,Versione),
	CONSTRAINT PK_T_BDGTestataSede PRIMARY KEY (BDGTestataSede_ID)
);

-- nbdo.app.T_BdgUPCalcolate definition

-- Drop table

-- DROP TABLE nbdo.app.T_BdgUPCalcolate;

CREATE TABLE nbdo.app.T_BdgUPCalcolate (
	BdgUPCalcolate_ID int IDENTITY(1,1) NOT NULL,
	Anno int NOT NULL,
	UnitaProduttive_ID int NOT NULL,
	PdMInput int NULL,
	DataOraCalcolo datetime NULL,
	Copia int NULL,
	CONSTRAINT PK_T_BdgUPCalcolate PRIMARY KEY (BdgUPCalcolate_ID),
	CONSTRAINT FK_T_BdgUPCalcolate_T_P_Man_Testata_Centrale_Nucleo FOREIGN KEY (PdMInput) REFERENCES nbdo.app.T_P_Man_Testata_Centrale_Nucleo(P_ManTestataCentraleNucleo_ID),
	CONSTRAINT FK_T_BdgUPCalcolate_T_UnitaProduttive FOREIGN KEY (UnitaProduttive_ID) REFERENCES nbdo.app.T_UnitaProduttive(UnitaProduttive_ID)
);
 CREATE NONCLUSTERED INDEX IX_T_BdgUPCalcolate_PdMInput_FK ON nbdo.app.T_BdgUPCalcolate (  PdMInput ASC  )  
	 WITH (  PAD_INDEX = OFF ,FILLFACTOR = 100  ,SORT_IN_TEMPDB = OFF , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ONLINE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON  )
	 ON [PRIMARY ] ;
 CREATE NONCLUSTERED INDEX IX_T_BdgUPCalcolate_UnitaProduttive_ID_FK ON nbdo.app.T_BdgUPCalcolate (  UnitaProduttive_ID ASC  )  
	 WITH (  PAD_INDEX = OFF ,FILLFACTOR = 100  ,SORT_IN_TEMPDB = OFF , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ONLINE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON  )
	 ON [PRIMARY ] ;

     -- nbdo.app.T_BdgUPApprovate definition

-- Drop table

-- DROP TABLE nbdo.app.T_BdgUPApprovate;

CREATE TABLE nbdo.app.T_BdgUPApprovate (
	BdgUPApprovate_ID int IDENTITY(1,1) NOT NULL,
	BdgTestataCentrale_ID int NOT NULL,
	UnitaProduttive_ID int NOT NULL,
	CONSTRAINT PK_T_BdgUPApprovate PRIMARY KEY (BdgUPApprovate_ID),
	CONSTRAINT FK_T_BdgUPApprovate_T_BDGTestataCentrale FOREIGN KEY (BdgTestataCentrale_ID) REFERENCES nbdo.app.T_BDGTestataCentrale(BDGTestataCentrale_ID),
	CONSTRAINT FK_T_BdgUPApprovate_T_UnitaProduttive FOREIGN KEY (UnitaProduttive_ID) REFERENCES nbdo.app.T_UnitaProduttive(UnitaProduttive_ID)
);
 CREATE NONCLUSTERED INDEX IX_T_BdgUPApprovate_BdgTestataCentrale_ID_FK ON nbdo.app.T_BdgUPApprovate (  BdgTestataCentrale_ID ASC  )  
	 WITH (  PAD_INDEX = OFF ,FILLFACTOR = 100  ,SORT_IN_TEMPDB = OFF , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ONLINE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON  )
	 ON [PRIMARY ] ;
 CREATE NONCLUSTERED INDEX IX_T_BdgUPApprovate_UnitaProduttive_ID_FK ON nbdo.app.T_BdgUPApprovate (  UnitaProduttive_ID ASC  )  
	 WITH (  PAD_INDEX = OFF ,FILLFACTOR = 100  ,SORT_IN_TEMPDB = OFF , IGNORE_DUP_KEY = OFF , STATISTICS_NORECOMPUTE = OFF , ONLINE = OFF , ALLOW_ROW_LOCKS = ON , ALLOW_PAGE_LOCKS = ON  )
	 ON [PRIMARY ] ;