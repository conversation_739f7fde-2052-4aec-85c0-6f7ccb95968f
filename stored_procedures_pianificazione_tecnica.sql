-- =============================================
-- STORED PROCEDURES COINVOLTE NEL PROCESSO DI PIANIFICAZIONE TECNICA
-- Sistema NBDO - A2A
-- Data creazione: 2025-01-15
-- =============================================

-- INDICE DELLE STORED PROCEDURE:
-- 1. PIANIFICAZIONE MANUTENZIONE (PMan)
-- 2. BUDGET COMBUSTIBILI
-- 3. REPORTING E ANALISI
-- 4. GESTIONE INTERVENTI
-- 5. UTILITY E SUPPORTO

-- =============================================
-- 1. PIANIFICAZIONE MANUTENZIONE (PMan)
-- =============================================

-- =============================================
-- app.PManInterventiSuiGruppi.LoadInterventiPianificati
-- Carica gli interventi di manutenzione pianificati per un'unità produttiva
-- =============================================
USE [nbdo]
GO
SET ANSI_NULLS ON
GO
SET QUOTED_IDENTIFIER ON
GO

CREATE PROCEDURE [app].[PManInterventiSuiGruppi.LoadInterventiPianificati]
@upID int, 
@testataID int, 
@utcDataDa datetime, 
@utcDataA datetime, 
@kpiId varchar(4000)
AS
BEGIN
	SET NOCOUNT ON;

	declare @siglaTipoStatistico varchar(10)
	--Sigla MOC
	set @siglaTipoStatistico = (SELECT SiglaTipoStatistico FROM T_TipoStatistico WHERE TipoStatistico_ID = 1)

  	SELECT 
	case when ig.DataOraInizioIntervento < @utcDataDa then @utcDataDa else ig.DataOraInizioIntervento end as OraInizioManutReale, 
	case when ig.DataOraFineIntervento >= @utcDataA then @utcDataA else ig.DataOraFineIntervento end as OraFineManutReale, 
	datediff(ss, ig.DataOraInizioIntervento, ig.DataOraFineIntervento) as OreDurataIntervento,  
	ig.DataOraInizioIntervento as DataOraEffetivoInizio, g.Gruppi_ID, g.Codice_Gruppo, 
	ig.P_ManInterventiSuiGruppi_ID, ig.CausaleIndisponibilita_ID, c.CodiceCausaleIndisp,  
	(CASE ig.TipoIntervento_ID WHEN 2 THEN 1 ELSE -1 END) as TipoStatistico_ID,  
	(CASE ig.TipoIntervento_ID WHEN 2 THEN @siglaTipoStatistico ELSE '-' END) as SiglaTipoStatistico 
	FROM T_P_Man_Interventi_sui_Gruppi ig 
	INNER JOIN T_KPICausaliAssociateIndisp kpi on ig.CausaleIndisponibilita_ID=kpi.CausaleIndisponibilita_ID 
	INNER JOIN app.fn_ParseCommaDelimitedList(@kpiId) kp on kpi.AnagraficaKPI_ID=kp.ItemID
	INNER JOIN T_Gruppi g ON g.Gruppi_ID = ig.Gruppi_ID 
	INNER JOIN T_CausaliIndisponibilita c ON c.CausaleIndisponibilita_ID = ig.CausaleIndisponibilita_ID 
	WHERE ig.P_ManTestataCentraleNucleo_ID = @testataID 
	AND ig.UnitaProduttive_ID = @upID
	AND ig.DataOraInizioIntervento is not null AND ig.DataOraFineIntervento is not null 
	AND ig.DataOraInizioIntervento <= @utcDataA AND @utcDataDa <= ig.DataOraFineIntervento
	ORDER BY 1
END
GO

-- =============================================
-- app.PManInterventiSuiGruppi.LoadInterventiNonAllocati
-- Carica gli interventi di manutenzione non ancora allocati temporalmente
-- =============================================
CREATE PROCEDURE [app].[PManInterventiSuiGruppi.LoadInterventiNonAllocati]
@upID int, 
@testataID int,
@kpiId varchar(4000)
AS
BEGIN
	SET NOCOUNT ON

	select PMaxDisp as Potenza, ResiduoOreNonAllocabili as Ore
	from T_P_Man_Interventi_sui_Gruppi a 
	inner join T_KPICausaliAssociateIndisp b on a.CausaleIndisponibilita_ID=b.CausaleIndisponibilita_ID 
	INNER JOIN app.fn_ParseCommaDelimitedList(@kpiId) kpi on b.AnagraficaKPI_ID=kpi.ItemID	
	where a.P_ManTestataCentraleNucleo_ID=@testataID 
	and a.UnitaProduttive_ID=@upID 
	and a.DataOraInizioIntervento is null and a.DataOraFineIntervento is null 
	and a.TipoIntervento_ID=1
	order by 1
END
GO

-- =============================================
-- app.PManCancellazionePianoInBozza
-- Cancella un piano di manutenzione in stato bozza
-- =============================================
CREATE PROCEDURE [app].[PManCancellazionePianoInBozza](@idPman int)
AS
BEGIN
	SET NOCOUNT ON;

  -- Schede Collegate
DELETE FROM T_P_Man_SKEventoCollegate WHERE P_ManInterventiSuiGruppi_ID in (
	SELECT P_ManInterventiSuiGruppi_ID FROM T_P_Man_Interventi_sui_Gruppi WHERE P_ManTestataCentraleNucleo_ID = @idPman
)

-- Schede Collegate Cancellate
DELETE FROM T_P_Man_SKEventoCollegateCancellate WHERE P_ManInterventiSuiGruppi_ID in (
	SELECT P_ManInterventiSuiGruppi_ID FROM T_P_Man_Interventi_sui_Gruppi WHERE P_ManTestataCentraleNucleo_ID = @idPman
)

-- Sfiori
DELETE FROM T_P_Man_InterventiSfiori WHERE P_ManInterventiSuiGruppi_ID in (
	SELECT P_ManInterventiSuiGruppi_ID FROM T_P_Man_Interventi_sui_Gruppi WHERE P_ManTestataCentraleNucleo_ID = @idPman
)

-- Interventi sui gruppi
DELETE FROM T_P_Man_Interventi_sui_Gruppi WHERE P_ManTestataCentraleNucleo_ID = @idPman

-- Testata centrale nucleo
DELETE FROM T_P_Man_Testata_Centrale_Nucleo WHERE P_ManTestataCentraleNucleo_ID = @idPman

END
GO

-- =============================================
-- app.PManCancellazionePianoInBozzaSede
-- Cancella un piano di manutenzione di sede in stato bozza
-- =============================================
CREATE PROCEDURE [app].[PManCancellazionePianoInBozzaSede](@idPman int)
AS

-- Cancellare anche dalla T_PManTestataCentraleSede
DELETE app.T_PManTestataCentraleSede
WHERE PManTestataSede_ID = @idPman AND IsArchived = 0;

-- Cancellazione del piano
DELETE app.T_P_Man_Testata_Sede
WHERE P_ManTestataSede_ID = @idPman;

GO

-- =============================================
-- app.sp_P_Man_NuovaVersione
-- Crea una nuova versione di un piano di manutenzione
-- =============================================
CREATE PROCEDURE [app].[sp_P_Man_NuovaVersione]
    @P_ManTestataCentraleNucleo_ID int,
    @UserOriginatore int,
    @Stati_ID int
AS
BEGIN
    DECLARE @DateCreation datetime
    DECLARE @Version int

    SET @DateCreation = GETUTCDATE()

    -- Ottiene la versione corrente
    SELECT @Version = Versione
    FROM T_P_Man_Testata_Centrale_Nucleo
    WHERE P_ManTestataCentraleNucleo_ID = @P_ManTestataCentraleNucleo_ID

    BEGIN TRANSACTION wk_new_version_PdM

    -- Crea una copia della testata con nuova versione
    INSERT INTO T_P_Man_Testata_Centrale_Nucleo
    (Anno_A, Anno_Da, Causale_Creazione, Centrali_ID, DataCreazione,
     Nuclei_ID, Stato, TipologiaEsercizio, Versione, User_Originatore)
    SELECT Anno_A, Anno_Da, Causale_Creazione, Centrali_ID, @DateCreation,
           Nuclei_ID, @Stati_ID, TipologiaEsercizio, (@Version + 1), @UserOriginatore
    FROM T_P_Man_Testata_Centrale_Nucleo
    WHERE P_ManTestataCentraleNucleo_ID = @P_ManTestataCentraleNucleo_ID

    -- Altre operazioni di copia...

    COMMIT TRANSACTION wk_new_version_PdM

    RETURN 0
END
GO

-- =============================================
-- 2. BUDGET COMBUSTIBILI
-- =============================================

-- =============================================
-- app.CombustibiliBudget.SALVA_T_BC_Contratti_PianoConsegne
-- Salva i contratti e il piano consegne per il budget combustibili
-- =============================================
CREATE PROCEDURE [app].[CombustibiliBudget.SALVA_T_BC_Contratti_PianoConsegne]
    @contrattoID int,
    @versionecentraleID int,
    @centraleID int,
    @annoDa int,
    @meseDa int,
    @annoA int,
    @meseA int
AS
BEGIN
    -- Salvataggio contratti combustibile e piano consegne
    -- Implementazione...
END
GO

-- =============================================
-- app.CombustibiliBudget.CreazioneBudget_1
-- Procedura principale per la creazione del budget combustibili
-- =============================================
CREATE PROCEDURE [app].[CombustibiliBudget.CreazioneBudget_1]
    @CentraleID int,
    @annoDa int,
    @meseDa int,
    @annoA int,
    @meseA int,
    @annoPrec int,
    @mesePrec int
AS
BEGIN
    CREATE TABLE #TempT1 (
        -- Struttura tabella temporanea
        Col1 int,
        Col2 varchar(50)
    )

    -- Inserisce dati da varie fonti
    INSERT INTO #TempT1
    EXEC [app].[CombustibiliBudget.CreazioneBudget_DatiDiConsuntivo]
        @CentraleID, @annoDa, @meseDa, @annoA, @meseA

    INSERT INTO #TempT1
    EXEC [app].[CombustibiliBudget.CreazioneBudget_DatiDiVersione]
        @CentraleID, @annoDa, @meseDa, @annoA, @meseA, @annoPrec, @mesePrec

    INSERT INTO #TempT1
    EXEC [app].[CombustibiliBudget.CreazioneBudget_DatiManuali]
        @CentraleID, @annoDa, @meseDa, @annoA, @meseA, @annoPrec, @mesePrec

    -- Restituisce i risultati
    SELECT * FROM #TempT1

    DROP TABLE #TempT1
END
GO

-- =============================================
-- app.CombustibiliBudget.Controlli
-- Esegue controlli di validità sui dati del budget combustibili
-- =============================================
CREATE PROCEDURE [app].[CombustibiliBudget.Controlli]
    @annoDa int,
    @meseDa int,
    @annoA int,
    @meseA int
AS
BEGIN
    CREATE TABLE #TempTab (Tab varchar(30), ErrorMsg varchar(100))

    -- Controlli PCI Centrale
    INSERT INTO #TempTab
    EXEC app.[CombustibiliBudget.ControlloPCICentrale] @annoDa, @meseDa, @annoA, @meseA

    -- Controlli Accisa
    INSERT INTO #TempTab
    EXEC app.[CombustibiliBudget.ControlloAccisa] @annoDa, @meseDa, @annoA, @meseA

    -- Controlli Prezzi
    INSERT INTO #TempTab
    EXEC app.[CombustibiliBudget.ControlloPrezzi] @annoDa, @meseDa, @annoA, @meseA

    -- Controlli Cambi
    INSERT INTO #TempTab
    EXEC app.[CombustibiliBudget.ControlloCambi] @annoDa, @meseDa, @annoA, @meseA

    SELECT * FROM #TempTab

    DROP TABLE #TempTab
END
GO

-- =============================================
-- app.CombustibiliBudget.GVC_SALVA_Contratti_PianoConsegne
-- Salva contratti e piano consegne per gestione versione centrale
-- =============================================
CREATE PROCEDURE [app].[CombustibiliBudget.GVC_SALVA_Contratti_PianoConsegne]
    @versionecentraleID int,
    @centraleID int,
    @annoDa int,
    @meseDa int,
    @annoA int,
    @meseA int
AS
BEGIN
    -- Gestione salvataggio contratti e piano consegne
    -- per versione centrale
    -- Implementazione...
END
GO

-- =============================================
-- 3. REPORTING E ANALISI
-- =============================================

-- =============================================
-- app.Reporting_GetInterventiManutenzione
-- Report principale per gli interventi di manutenzione
-- =============================================
CREATE PROCEDURE [app].[Reporting_GetInterventiManutenzione]
    @IdCentrale INT,
    @DataDa DATETIME,
    @DataA DATETIME,
    @ListaAnni VARCHAR(200) = ''
AS
BEGIN
    -- Tutte le Centrali
    IF (@IdCentrale = 1)
    BEGIN
        EXEC [app].[Reporting_GetInterventiManutenzioneCentraliTutte] @DataDa, @DataA, @ListaAnni
    END

    -- Tutte le Termo
    ELSE IF (@IdCentrale = 2)
    BEGIN
        EXEC Reporting_GetInterventiManutenzioneTermoTutte @DataDa, @DataA, @ListaAnni
    END

    -- Tutte le Idro
    ELSE IF (@IdCentrale = 3)
    BEGIN
        EXEC Reporting_GetInterventiManutenzioneIdroTutte @DataDa, @DataA, @ListaAnni
    END

    -- Selezione singola
    ELSE
    BEGIN
        DECLARE @isIdro int = (SELECT COUNT(*) FROM T_Nuclei WHERE Nuclei_ID = @IdCentrale)
        IF (@isIdro > 0)
        BEGIN
            EXEC Reporting_GetInterventiManutenzioneIdro @IdCentrale, @DataDa, @DataA, @ListaAnni
        END
        ELSE
        BEGIN
            EXEC Reporting_GetInterventiManutenzioneTermo @IdCentrale, @DataDa, @DataA, @ListaAnni
        END
    END
END
GO

-- =============================================
-- app.Reporting_InterventiTollingManAnalisi
-- Analisi degli interventi di manutenzione per tolling
-- =============================================
CREATE PROCEDURE [app].[Reporting_InterventiTollingManAnalisi]
    @pianoTollingID as int,
    @gruppoID as nvarchar(4000),
    @unitaProduttivaID as nvarchar(4000),
    @centraleID as nvarchar(4000),
    @nucleoID as nvarchar(4000),
    @tipoIntervento as nvarchar(4000),
    @utcDataInizio as datetime,
    @utcDataFine as datetime,
    @Suffisso as nvarchar(4000),
    @sortExpression as nvarchar(4000)
AS
BEGIN
    -- Analisi interventi tolling manutenzione
    -- Implementazione...
END
GO

-- =============================================
-- app.Reporting_KPIBudgetVsManutenzione_Testata
-- Report KPI confronto Budget vs Manutenzione
-- =============================================
CREATE PROCEDURE [app].[Reporting_KPIBudgetVsManutenzione_Testata]
    @parametri varchar(4000)
AS
BEGIN
    -- Testata report KPI Budget vs Manutenzione
    -- Implementazione...
END
GO

-- =============================================
-- app.Reporting_PianoManutAutomatico
-- Report per piano manutenzione automatico
-- =============================================
CREATE PROCEDURE [app].[Reporting_PianoManutAutomatico]
    @parametri varchar(4000)
AS
BEGIN
    -- Report piano manutenzione automatico
    -- Implementazione...
END
GO

-- =============================================
-- 4. GESTIONE INTERVENTI
-- =============================================

-- =============================================
-- app.MOCC_GetElenco
-- Ottiene l'elenco degli interventi MOCC (Manutenzione Ordinaria Ciclica Centralizzata)
-- =============================================
CREATE PROCEDURE [app].[MOCC_GetElenco]
AS
BEGIN
    SELECT TPM.P_ManInterventiSuiGruppi_ID,
           TPM.Gruppi_ID,
           TPM.CausaleIndisponibilita_ID,
           TPM.DataOraInizioIntervento,
           TPM.DataOraFineIntervento,
           ST.Descrizione as StatoDescrizione
    FROM T_P_Man_Interventi_sui_Gruppi TPM
    INNER JOIN app.T_Stati ST ON TPM.StatoInt = ST.Stati_ID
    WHERE TPM.TipoIntervento_ID = 2
    AND TPM.StatoInt = 166
    AND TPM.P_ManTestataCentraleNucleo_ID IN
    (
        SELECT P_ManTestataCentraleNucleo_ID
        FROM T_P_Man_Testata_Centrale_Nucleo
        WHERE Stato = 34 AND Anno_A >= YEAR(GETUTCDATE())
        AND P_ManTestataCentraleNucleo_ID IN (
            SELECT PManTestataCentraleNucleo_ID
            FROM t_pmantestatacentralesede
            WHERE isarchived = 0
        )
    )
    AND TPM.P_ManInterventiSuiGruppi_ID NOT IN
    (
        SELECT P_ManInterventiSuiGruppi_ID
        FROM T_SKCEventiManutenzione
    )
END
GO

-- =============================================
-- app.SelezionaInterventi
-- Seleziona interventi di manutenzione in base ai criteri
-- =============================================
CREATE PROCEDURE [app].[SelezionaInterventi]
    @parametri varchar(4000)
AS
BEGIN
    -- Selezione interventi manutenzione
    -- Implementazione...
END
GO

-- =============================================
-- app.SelezionaInterventiByGruppi
-- Seleziona interventi di manutenzione per gruppi specifici
-- =============================================
CREATE PROCEDURE [app].[SelezionaInterventiByGruppi]
    @gruppiID varchar(4000)
AS
BEGIN
    -- Selezione interventi per gruppi
    -- Implementazione...
END
GO

-- =============================================
-- 5. UTILITY E SUPPORTO
-- =============================================

-- =============================================
-- app.UnitaEssenziali.Elaborazioni.GetProgrammiMustRun
-- Ottiene i programmi Must Run per le unità essenziali
-- =============================================
CREATE PROCEDURE [app].[UnitaEssenziali.Elaborazioni.GetProgrammiMustRun]
    @flagSelezione int,
    @ups varchar(1000)='',
    @pgmMustRunTestataID int = null,
    @utcDataRif datetime
AS
BEGIN
    IF (@flagSelezione = 1)
    BEGIN
        SELECT T1.*,
               CASE WHEN T1.FlagManuale = 0 THEN 'NO' ELSE 'SI' END as FlagManuale1
        FROM T_PgmMustRun T1
        INNER JOIN T_PgmMustRunTestata T2 ON T1.PgmMustRunTestata_Id = T2.PgmMustRunTestata_Id
        WHERE T2.GiornoDiRiferimento = @utcDataRif
    END
    ELSE IF (@flagSelezione = 2)
    BEGIN
        SELECT T1.*,
               CASE WHEN T1.FlagManuale = 0 THEN 'NO' ELSE 'SI' END as FlagManuale1
        FROM T_PgmMustRun T1
        WHERE T1.PgmMustRunTestata_Id IN
        (
            SELECT T1.PgmMustRunTestata_Id
            FROM T_PgmMustRunTestata T1
            INNER JOIN (SELECT ItemID as UnitaProduttiva_ID
                       FROM app.fn_ParseCommaDelimitedList(@ups)) T2
                       ON T2.UnitaProduttiva_ID = T1.UnitaProduttiva_id
            WHERE T1.GiornoDiRiferimento = @utcDataRif
        )
    END
    ELSE IF (@flagSelezione = 3)
    BEGIN
        SELECT T1.*,
               CASE WHEN T1.FlagManuale = 0 THEN 'NO' ELSE 'SI' END as FlagManuale1
        FROM T_PgmMustRun T1
        WHERE T1.PgmMustRunTestata_Id = @pgmMustRunTestataID
    END
END
GO

-- =============================================
-- app.GetEventiManutenzioneByData
-- Ottiene gli eventi di manutenzione per una data specifica
-- =============================================
CREATE PROCEDURE [app].[GetEventiManutenzioneByData]
    @dataRiferimento datetime
AS
BEGIN
    SELECT *
    FROM T_P_Man_Interventi_sui_Gruppi
    WHERE @dataRiferimento BETWEEN DataOraInizioIntervento AND DataOraFineIntervento
    ORDER BY DataOraInizioIntervento
END
GO

-- =============================================
-- app.FindTabellePiene
-- Utility per trovare le tabelle con dati nel database
-- =============================================
CREATE PROCEDURE [app].[FindTabellePiene]
AS
BEGIN
    DECLARE @tabella varchar(100)
    DECLARE @rec CURSOR

    DECLARE @nrrecords int
    CREATE TABLE #outres
    (
        nometabella varchar(100) COLLATE SQL_Latin1_General_CP1_CI_AS,
        nr int
    )

    SET @rec=CURSOR LOCAL SCROLL FOR
        SELECT TABLE_NAME
        FROM INFORMATION_SCHEMA.TABLES
        WHERE TABLE_TYPE = 'BASE TABLE'

    OPEN @rec
    FETCH NEXT FROM @rec INTO @tabella

    WHILE (@@FETCH_STATUS <> -1)
    BEGIN
        EXEC (
            'INSERT INTO #outres (nr, nometabella) SELECT COUNT(*) as nr, '''+ @tabella + ''' FROM '+ @tabella
        )
        FETCH NEXT FROM @rec INTO @tabella
    END

    CLOSE @rec
    DEALLOCATE @rec

    SELECT * FROM #outres WHERE nr > 0 ORDER BY nr DESC

    DROP TABLE #outres
END
GO

-- =============================================
-- FINE STORED PROCEDURES
-- =============================================
